import {PermissionType, EditionInfo} from '@api/auth';
import {Privilege} from '@api/permission/type';
import {createSlice} from '@reduxjs/toolkit';

export enum PageStatus {
  Valid = 'valid',
  NoPagePermission = 'noPagePermission',
  NoWorkspacePermission = 'noWorkspacePermission'
}

export interface IGlobalAuthState {
  workspace: PermissionType;
  metastore: PermissionType;
  editionInfo: EditionInfo;
  workspacePermission: Partial<Record<Privilege, boolean>>;
  globalPermission: Partial<Record<Privilege, boolean>>;
  systemAdmin: boolean; // 是否是系统管理员
  pageStatus: PageStatus;
  availableInChengdu: boolean; // 是否在成都可用
  hasFullControl: boolean; // 是否有 DataBuilderFullControl 权限
}

const initialState: IGlobalAuthState = {
  workspace: undefined,
  metastore: undefined,
  editionInfo: undefined,
  workspacePermission: null,
  globalPermission: null,
  systemAdmin: false,
  pageStatus: PageStatus.Valid,
  availableInChengdu: true,
  hasFullControl: false
};

const globalAuthSlice = createSlice({
  name: 'globalAuth',
  initialState,
  reducers: {
    updateGlobalPermission: (state, action) => {
      state.globalPermission = action.payload;
    },
    updateWorkspacePermission: (state, action) => {
      state.workspacePermission = action.payload;
    },
    updateSystemAdmin: (state, action) => {
      state.systemAdmin = action.payload;
    },
    updatePageStatus: (state, action) => {
      state.pageStatus = action.payload;
    },
    updateEditionInfo: (state, action) => {
      state.editionInfo = action.payload;
    },
    updateAvailableInChengdu: (state, action) => {
      state.availableInChengdu = action.payload;
    },
    updateHasFullControl: (state, action) => {
      state.hasFullControl = action.payload;
    }
  }
});

export const {updateGlobalPermission, updateWorkspacePermission, updateEditionInfo, updateHasFullControl} =
  globalAuthSlice.actions;

export default globalAuthSlice.reducer;
