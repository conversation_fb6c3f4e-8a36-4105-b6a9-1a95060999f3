# PlaygroundAdminCheck 组件

## 功能说明

`PlaygroundAdminCheck` 组件用于检查当前用户是否为 playground 用户且具有 DataBuilderFullControl 权限。只有满足这两个条件的用户才能看到被包裹的内容。

## 实现原理

1. **用户身份检查**: 通过 `useCookieState('bce-login-accountid')` 获取当前用户的账户ID，与 `playgroundInfo.accountId` 比较判断是否为 playground 用户。

2. **权限检查**: 使用 `useHasFullControl` hook 获取用户是否具有 DataBuilderFullControl 权限。该权限状态在 App.tsx 初始化时通过调用 `checkFullControl` API 获取并存储在 Redux store 中。

3. **权限获取流程**:
   - 在 `App.tsx` 的 `init()` 函数中，如果检测到用户是 playground 用户
   - 调用 `checkFullControl(true)` API（开启 silent 模式）
   - 如果 API 调用成功，将 `hasFullControl` 设置为 `true`
   - 如果 API 调用失败或异常，将 `hasFullControl` 设置为 `false`
   - 权限状态存储在 `globalAuthSlice.hasFullControl` 中

## 使用方法

```tsx
import PlaygroundAdminCheck from '@components/PlaygroundAdminCheck';

// 基本使用
<PlaygroundAdminCheck>
  <div>只有 playground 管理员才能看到这个内容</div>
</PlaygroundAdminCheck>

// 带 fallback 的使用
<PlaygroundAdminCheck fallback={<div>您没有权限查看此内容</div>}>
  <div>管理员专用功能</div>
</PlaygroundAdminCheck>
```

## 相关文件

- `src/store/GlobalAuth.ts`: 定义了 `hasFullControl` 状态和相关 reducer
- `src/hooks/useHasFullControl.ts`: 提供获取权限状态的 hook
- `src/App.tsx`: 在应用初始化时获取并设置权限状态
- `src/api/auth.ts`: 定义了 `checkFullControl` API 函数

## API 说明

### Props

| 属性 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| children | React.ReactNode | 是 | - | 有权限时显示的内容 |
| fallback | React.ReactNode | 否 | null | 无权限时显示的内容 |

### useHasFullControl Hook

```tsx
import useHasFullControl from '@hooks/useHasFullControl';

const MyComponent = () => {
  const hasFullControl = useHasFullControl();
  
  return (
    <div>
      {hasFullControl ? '您是管理员' : '您不是管理员'}
    </div>
  );
};
```
