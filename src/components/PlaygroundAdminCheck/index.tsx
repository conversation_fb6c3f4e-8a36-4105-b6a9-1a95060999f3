import React from 'react';
import useHasFullControl from '@hooks/useHasFullControl';
import {useCookieState} from 'ahooks';
import {playgroundInfo} from '@helpers/palyground-info';

interface PlaygroundAdminCheckProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * Playground管理员权限检查组件
 * 只有playground用户且具有DataBuilderFullControl权限时才显示children
 * 其他情况显示fallback或null
 */
const PlaygroundAdminCheck: React.FC<PlaygroundAdminCheckProps> = ({
  children,
  fallback = null
}) => {
  const [accountid] = useCookieState('bce-login-accountid');
  const isPlayGroundUser = accountid === playgroundInfo.accountId;
  const hasFullControl = useHasFullControl();

  // 只有playground用户且有DataBuilderFullControl权限时才显示children
  if (isPlayGroundUser && hasFullControl) {
    return <>{children}</>;
  }

  return <>{fallback}</>;
};

export default PlaygroundAdminCheck;
