{"parser": "@typescript-eslint/parser", "env": {"node": true, "browser": true, "commonjs": true, "es2021": true}, "extends": ["plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "eslint:recommended", "plugin:prettier/recommended"], "plugins": ["@typescript-eslint", "react", "react-hooks", "prettier", "simple-import-sort"], "globals": {"APP_TITLE": "readonly", "APP_ENABLE_I18N": "readonly", "APP_ENABLE_INDEPENDENT_I18N": "readonly", "APP_ALLOWED_LANGUAGE_TYPES": "readonly", "APP_IS_EMBED_MODE": "readonly", "__webpack_public_path__": "writable", "IS_DEV_ENV": "readonly"}, "rules": {"object-curly-spacing": ["error", "never"], "comma-dangle": ["error", "never"], "@typescript-eslint/no-explicit-any": "off", "no-unused-vars": "off", "react/react-in-jsx-scope": "off", "react/jsx-uses-react": "off", "@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/ban-ts-comment": "off"}, "settings": {"react": {"createClass": "createReactClass", "pragma": "React", "fragment": "Fragment", "version": "detect", "flowVersion": "0.53"}}, "overrides": [{"files": ["**/*.ts", "**/*.tsx"], "plugins": ["@typescript-eslint", "react", "react-hooks", "prettier", "simple-import-sort"], "rules": {"no-restricted-imports": ["error", {"paths": [{"name": "@baidu/bce-react-toolkit", "importNames": ["request", "useRegion"], "message": "请从 src/api/apiFunction.tsx 导入 request，src/hooks/useRegion.ts 导入 useRegion"}]}]}}]}